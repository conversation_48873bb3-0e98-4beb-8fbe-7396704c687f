// Code Window Shared Component Styles
.code-window-shared-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  width: 100%;
  position: relative;
  background-color: var(--theme-background, #ffffff);
  color: var(--theme-text, #1f2937);
  transition: background-color 0.3s ease, color 0.3s ease;

  // Header Section
  .shared-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0.75rem 1rem;
    border-bottom: 1px solid var(--theme-border, #e5e7eb);
    background-color: var(--theme-surface, #f9fafb);
    min-height: 60px;
    z-index: 10;

    .header-left,
    .header-right {
      display: flex;
      align-items: center;
      gap: 0.5rem;
      flex: 0 0 auto;
    }

    .header-center {
      display: flex;
      align-items: center;
      justify-content: center;
      flex: 1;
      min-width: 0;

      .project-name {
        font-weight: 600;
        font-size: 1rem;
        color: var(--theme-text, #1f2937);
        text-align: center;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        max-width: 300px;
      }
    }

    .theme-toggle-btn {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 36px;
      height: 36px;
      border: none;
      border-radius: 6px;
      background-color: transparent;
      color: var(--theme-textSecondary, #6b7280);
      cursor: pointer;
      transition: all 0.2s ease;

      &:hover:not(:disabled) {
        background-color: var(--theme-border, #e5e7eb);
        color: var(--theme-text, #1f2937);
      }

      &:disabled {
        opacity: 0.5;
        cursor: not-allowed;
      }

      i {
        font-size: 1.1rem;
      }
    }
  }

  // Tab Navigation
  .tab-navigation {
    border-bottom: 1px solid var(--theme-border, #e5e7eb);
    background-color: var(--theme-background, #ffffff);
    z-index: 9;

    .tabs-container {
      display: flex;
      align-items: center;
      padding: 0 1rem;
      gap: 0.25rem;

      .tab-button {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        padding: 0.75rem 1rem;
        border: none;
        background-color: transparent;
        color: var(--theme-textSecondary, #6b7280);
        font-size: 0.875rem;
        font-weight: 500;
        cursor: pointer;
        border-radius: 6px 6px 0 0;
        position: relative;
        transition: all 0.2s ease;
        min-height: 44px;

        &:hover:not(:disabled):not(.active) {
          background-color: var(--theme-border, #e5e7eb);
          color: var(--theme-text, #1f2937);
        }

        &.active {
          background-color: var(--theme-background, #ffffff);
          color: var(--theme-accent, #3b82f6);
          border-bottom: 2px solid var(--theme-accent, #3b82f6);

          &::after {
            content: '';
            position: absolute;
            bottom: -1px;
            left: 0;
            right: 0;
            height: 2px;
            background-color: var(--theme-accent, #3b82f6);
          }
        }

        &.disabled {
          opacity: 0.5;
          cursor: not-allowed;
          color: var(--theme-textSecondary, #6b7280);
        }

        &.loading {
          pointer-events: none;
        }

        i {
          font-size: 1rem;
        }

        .loading-spinner {
          width: 16px;
          height: 16px;
          border: 2px solid var(--theme-border, #e5e7eb);
          border-top: 2px solid var(--theme-accent, #3b82f6);
          border-radius: 50%;
          animation: spin 1s linear infinite;
        }
      }
    }
  }

  // Content Area
  .content-area {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    position: relative;
  }

  // Loading Overlay
  .loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(255, 255, 255, 0.9);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    backdrop-filter: blur(2px);

    .loading-content {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 1rem;
      padding: 2rem;
      border-radius: 8px;
      background-color: var(--theme-background, #ffffff);
      box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);

      .loading-spinner {
        &.large {
          width: 40px;
          height: 40px;
          border: 3px solid var(--theme-border, #e5e7eb);
          border-top: 3px solid var(--theme-accent, #3b82f6);
          border-radius: 50%;
          animation: spin 1s linear infinite;
        }
      }

      .loading-text {
        font-size: 0.875rem;
        color: var(--theme-textSecondary, #6b7280);
        text-align: center;
      }
    }
  }

  // Error Display
  .error-display {
    position: absolute;
    top: 1rem;
    right: 1rem;
    max-width: 400px;
    z-index: 1001;

    .error-content {
      display: flex;
      align-items: center;
      gap: 0.75rem;
      padding: 0.75rem 1rem;
      background-color: var(--theme-error, #ef4444);
      color: white;
      border-radius: 6px;
      box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
      animation: slideInRight 0.3s ease-out;

      .error-icon {
        font-size: 1.25rem;
        flex-shrink: 0;
      }

      .error-text {
        flex: 1;
        font-size: 0.875rem;
        line-height: 1.4;
      }

      .error-dismiss {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 24px;
        height: 24px;
        border: none;
        background-color: transparent;
        color: white;
        cursor: pointer;
        border-radius: 4px;
        transition: background-color 0.2s ease;

        &:hover {
          background-color: rgba(255, 255, 255, 0.2);
        }

        i {
          font-size: 1rem;
        }
      }
    }
  }

  // Dark theme overrides
  &.dark-theme {
    .loading-overlay {
      background-color: rgba(31, 41, 55, 0.9);
    }

    .shared-header {
      border-bottom-color: var(--theme-border, #4b5563);
    }

    .tab-navigation {
      border-bottom-color: var(--theme-border, #4b5563);
    }
  }
}

// Animations
@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

@keyframes slideInRight {
  0% {
    transform: translateX(100%);
    opacity: 0;
  }
  100% {
    transform: translateX(0);
    opacity: 1;
  }
}

// Responsive design
@media (max-width: 768px) {
  .code-window-shared-container {
    .shared-header {
      padding: 0.5rem;
      min-height: 50px;

      .header-center .project-name {
        max-width: 200px;
        font-size: 0.875rem;
      }

      .theme-toggle-btn {
        width: 32px;
        height: 32px;
      }
    }

    .tab-navigation .tabs-container {
      padding: 0 0.5rem;

      .tab-button {
        padding: 0.5rem 0.75rem;
        font-size: 0.8rem;
        min-height: 40px;

        span {
          display: none;
        }

        i {
          font-size: 1.1rem;
        }
      }
    }

    .error-display {
      top: 0.5rem;
      right: 0.5rem;
      left: 0.5rem;
      max-width: none;
    }
  }
}

// High contrast mode support
@media (prefers-contrast: high) {
  .code-window-shared-container {
    .tab-button {
      &.active {
        border: 2px solid var(--theme-accent, #3b82f6);
      }
    }

    .theme-toggle-btn {
      border: 1px solid var(--theme-border, #e5e7eb);
    }
  }
}

// Reduced motion support
@media (prefers-reduced-motion: reduce) {
  .code-window-shared-container {
    * {
      transition: none !important;
      animation: none !important;
    }
  }
}
