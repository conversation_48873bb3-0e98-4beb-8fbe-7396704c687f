import { 
  Component, 
  Input, 
  Output, 
  EventEmitter, 
  OnInit, 
  OnD<PERSON>roy, 
  ChangeDetectionStrategy,
  inject,
  signal,
  computed
} from '@angular/core';
import { CommonModule } from '@angular/common';
import { Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';

import { 
  ViewType, 
  ThemeType, 
  FileModel,
  TabTransitionState,
  ComponentState
} from './interfaces/code-window-shared.interfaces';

import { CodeWindowSharedStateService } from './services/code-window-shared-state.service';
import { CodeWindowSharedThemeService } from './services/code-window-shared-theme.service';
import { CodeWindowSharedUtilsService } from './services/code-window-shared-utils.service';

@Component({
  selector: 'app-code-window-shared',
  standalone: true,
  imports: [CommonModule],
  template: `
    <div 
      class="code-window-shared-container"
      [class]="themeService.themeClass()"
      [attr.data-theme]="themeService.currentTheme()"
    >
      <!-- Header Section -->
      <div class="shared-header" *ngIf="showHeader">
        <div class="header-left">
          <ng-content select="[slot=header-left]"></ng-content>
        </div>
        
        <div class="header-center">
          <div class="project-name" *ngIf="projectName && !shouldHideProjectName">
            {{ projectName }}
          </div>
          <ng-content select="[slot=header-center]"></ng-content>
        </div>
        
        <div class="header-right">
          <button 
            *ngIf="showThemeToggle"
            class="theme-toggle-btn"
            (click)="toggleTheme()"
            [disabled]="themeService.isTransitioning()"
            [title]="getThemeToggleTitle()"
          >
            <i [class]="getThemeIcon()"></i>
          </button>
          <ng-content select="[slot=header-right]"></ng-content>
        </div>
      </div>

      <!-- Tab Navigation -->
      <div class="tab-navigation" *ngIf="showTabs">
        <div class="tabs-container">
          <button
            *ngFor="let tab of availableTabs"
            class="tab-button"
            [class.active]="isTabActive(tab.id)"
            [class.disabled]="tab.disabled"
            [class.loading]="tab.loading"
            [disabled]="tab.disabled || stateService.tabTransitionInProgress() || tab.loading"
            (click)="onTabClick(tab.id)"
            [title]="tab.tooltip"
          >
            <i [class]="tab.icon" *ngIf="tab.icon"></i>
            <span>{{ tab.label }}</span>
            <div class="loading-spinner" *ngIf="tab.loading"></div>
          </button>
        </div>
      </div>

      <!-- Content Area -->
      <div class="content-area">
        <ng-content></ng-content>
      </div>

      <!-- Loading Overlay -->
      <div class="loading-overlay" *ngIf="showLoadingOverlay">
        <div class="loading-content">
          <div class="loading-spinner large"></div>
          <div class="loading-text">{{ loadingMessage }}</div>
        </div>
      </div>

      <!-- Error Display -->
      <div class="error-display" *ngIf="errorMessage">
        <div class="error-content">
          <i class="error-icon bi bi-exclamation-triangle"></i>
          <div class="error-text">{{ errorMessage }}</div>
          <button class="error-dismiss" (click)="dismissError()">
            <i class="bi bi-x"></i>
          </button>
        </div>
      </div>
    </div>
  `,
  styleUrls: ['./code-window-shared.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class CodeWindowSharedComponent implements OnInit, OnDestroy {
  // Injected services
  readonly stateService = inject(CodeWindowSharedStateService);
  readonly themeService = inject(CodeWindowSharedThemeService);
  readonly utilsService = inject(CodeWindowSharedUtilsService);

  // Inputs
  @Input() projectName: string | null = null;
  @Input() showHeader: boolean = true;
  @Input() showTabs: boolean = true;
  @Input() showThemeToggle: boolean = true;
  @Input() shouldHideProjectName: boolean = false;
  @Input() loadingMessage: string = 'Loading...';
  @Input() errorMessage: string | null = null;
  @Input() availableTabs: TabConfig[] = [];

  // Outputs
  @Output() tabChanged = new EventEmitter<string>();
  @Output() themeChanged = new EventEmitter<ThemeType>();
  @Output() errorDismissed = new EventEmitter<void>();
  @Output() headerAction = new EventEmitter<string>();

  // Component state
  private destroy$ = new Subject<void>();
  
  // Signals
  readonly isLoading = signal<boolean>(false);
  readonly currentTab = signal<string>('preview');
  readonly hasError = signal<boolean>(false);

  // Computed properties
  readonly showLoadingOverlay = computed(() => 
    this.isLoading() || this.stateService.isComponentLoading()
  );

  readonly currentThemeClass = computed(() => 
    this.themeService.themeClass()
  );

  ngOnInit(): void {
    this.initializeComponent();
    this.setupStateSubscriptions();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  // Public methods
  toggleTheme(): void {
    this.themeService.toggleTheme();
  }

  onTabClick(tabId: string): void {
    if (this.stateService.tabTransitionInProgress()) {
      return;
    }

    this.stateService.setTabTransitionInProgress(true);
    this.currentTab.set(tabId);
    
    // Update state based on tab
    this.updateStateForTab(tabId);
    
    // Emit tab change event
    this.tabChanged.emit(tabId);

    // Complete transition after a short delay
    setTimeout(() => {
      this.stateService.setTabTransitionInProgress(false);
    }, 300);
  }

  dismissError(): void {
    this.hasError.set(false);
    this.errorDismissed.emit();
  }

  isTabActive(tabId: string): boolean {
    return this.currentTab() === tabId;
  }

  getThemeToggleTitle(): string {
    const currentTheme = this.themeService.currentTheme();
    return `Switch to ${currentTheme === 'light' ? 'dark' : 'light'} theme`;
  }

  getThemeIcon(): string {
    const currentTheme = this.themeService.currentTheme();
    return currentTheme === 'light' ? 'bi bi-moon' : 'bi bi-sun';
  }

  // State management methods
  setLoading(loading: boolean): void {
    this.isLoading.set(loading);
    this.stateService.setLoading(loading);
  }

  setError(error: string | null): void {
    this.hasError.set(!!error);
    // Error handling can be extended here
  }

  updateComponentState(state: Partial<ComponentState>): void {
    this.stateService.setComponentState(state);
  }

  // Utility methods
  generateId(): string {
    return this.utilsService.generateUniqueId();
  }

  formatTimestamp(timestamp: Date | string | number): string {
    return this.utilsService.formatTimestamp(timestamp);
  }

  // Private methods
  private initializeComponent(): void {
    // Set initial theme
    this.themeService.updateCSSCustomProperties();
    
    // Initialize default tabs if none provided
    if (this.availableTabs.length === 0) {
      this.availableTabs = this.getDefaultTabs();
    }

    // Set initial tab
    const activeTab = this.availableTabs.find(tab => tab.active)?.id || 'preview';
    this.currentTab.set(activeTab);
  }

  private setupStateSubscriptions(): void {
    // Subscribe to theme changes
    this.themeService.themeObservable
      .pipe(takeUntil(this.destroy$))
      .subscribe(theme => {
        this.themeChanged.emit(theme);
      });

    // Subscribe to tab state changes
    this.stateService.currentTabState
      .pipe(takeUntil(this.destroy$))
      .subscribe(tabState => {
        this.currentTab.set(tabState.activeTab);
      });

    // Subscribe to loading state changes
    this.stateService.isLoading
      .pipe(takeUntil(this.destroy$))
      .subscribe(loading => {
        this.isLoading.set(loading);
      });
  }

  private updateStateForTab(tabId: string): void {
    // Reset all tab states
    this.stateService.setCodeActive(false);
    this.stateService.setPreviewActive(false);
    this.stateService.setArtifactsActive(false);
    this.stateService.setHistoryActive(false);

    // Set the active tab
    switch (tabId) {
      case 'code':
      case 'editor':
        this.stateService.setCodeActive(true);
        this.stateService.setCurrentView('editor');
        break;
      case 'preview':
        this.stateService.setPreviewActive(true);
        this.stateService.setCurrentView('preview');
        break;
      case 'artifacts':
        this.stateService.setArtifactsActive(true);
        this.stateService.setCurrentView('artifacts');
        break;
      case 'history':
        this.stateService.setHistoryActive(true);
        break;
      case 'overview':
        this.stateService.setPreviewActive(true);
        this.stateService.setCurrentView('overview');
        break;
      default:
        this.stateService.setPreviewActive(true);
        this.stateService.setCurrentView('preview');
        break;
    }

    // Update tab state
    this.stateService.setCurrentTabState({
      activeTab: tabId,
      isTransitioning: true,
      lastError: null
    });
  }

  private getDefaultTabs(): TabConfig[] {
    return [
      {
        id: 'preview',
        label: 'Preview',
        icon: 'bi bi-eye',
        active: true,
        disabled: false,
        loading: false,
        tooltip: 'Preview the application'
      },
      {
        id: 'code',
        label: 'Code',
        icon: 'bi bi-code-slash',
        active: false,
        disabled: false,
        loading: false,
        tooltip: 'View generated code'
      },
      {
        id: 'artifacts',
        label: 'Artifacts',
        icon: 'bi bi-file-earmark-text',
        active: false,
        disabled: false,
        loading: false,
        tooltip: 'View artifacts and logs'
      }
    ];
  }
}

// Tab configuration interface
export interface TabConfig {
  id: string;
  label: string;
  icon?: string;
  active: boolean;
  disabled: boolean;
  loading: boolean;
  tooltip: string;
}
