import { SafeHtml, SafeResourceUrl } from '@angular/platform-browser';

// Core view types
export type ViewType = 'loading' | 'editor' | 'preview' | 'overview' | 'logs' | 'artifacts';
export type ThemeType = 'light' | 'dark';
export type IconStatus = 'default' | 'active' | 'disable';

// File-related interfaces
export interface FileModel {
  name: string;
  type: 'file' | 'folder';
  content?: string;
  children?: FileModel[];
  expanded?: boolean;
  fileName?: string;
  path?: string;
  language?: string;
}

export interface FileData {
  path: string;
  code: string;
}

// Tab state interfaces
export interface PreviewTabState {
  isVisible: boolean;
  isEnabled: boolean;
  isLoading: boolean;
  hasError: boolean;
  errorMessage?: string;
  loadingMessage?: string;
  tooltipMessage: string;
}

export interface CodeTabState {
  isVisible: boolean;
  isEnabled: boolean;
  isLoading: boolean;
  hasError: boolean;
  errorMessage?: string;
  loadingMessage?: string;
  tooltipMessage: string;
}

export interface TabTransitionState {
  activeTab: string;
  isTransitioning: boolean;
  lastError: string | null;
}

export enum PreviewTabStatus {
  HIDDEN = 'hidden',
  DISABLED = 'disabled',
  LOADING = 'loading',
  ENABLED = 'enabled',
  ERROR = 'error'
}

// UI Design interfaces
export interface UIDesignAPIResponse {
  pageName: string;
  content: string;
}

export interface WireframeAPIResponse {
  fileName: string;
  content: string;
}

export interface UIDesignPageData {
  fileName: string;
  content: string;
}

export interface UIDesignNodeData {
  title: string;
  displayTitle?: string;
  htmlContent: SafeHtml;
  rawContent: string;
  width: number;
  height: number;
  isLoading: boolean;
  loadingMessage?: string;
  originalNodeId?: string;
  zIndex?: number;
}

export interface UIDesignNodePosition {
  x: number;
  y: number;
}

export interface UIDesignNode {
  id: string;
  type: 'ui-design';
  data: UIDesignNodeData;
  position: UIDesignNodePosition;
  selected: boolean;
  dragging: boolean;
  visible: boolean;
}

export interface UIDesignPage {
  fileName: string;
  content: string;
}

// Design token interfaces
export interface DesignToken {
  id: string;
  name: string;
  value: string;
  type: 'color' | 'typography' | 'spacing' | 'size';
  category: string;
  editable: boolean;
  description?: string;
}

export interface DesignTokenEditState {
  isEditMode: boolean;
  editButtonText: string;
  hasUnsavedChanges: boolean;
  originalValues: Map<string, string>;
}

export interface DesignTokenLoadingState {
  isLoading: boolean;
  showAnalyzingAnimation: boolean;
  hasReceivedTokens: boolean;
  loadingMessage: string;
}

// State management interfaces
export interface LoadingStates {
  isUIDesignLoading: boolean;
  isCodeGenerationLoading: boolean;
  isPreviewLoading: boolean;
  isArtifactsLoading: boolean;
  isRegenerationInProgress: boolean;
}

export interface TabState {
  activeTab: string;
  isTransitioning: boolean;
  lastError?: string;
}

export interface IntroMessageState {
  isLoading: boolean;
  text: string;
  isTyping: boolean;
  hasError: boolean;
  shouldReplaceText: boolean;
  introAPICompleted: boolean;
  mainAPIInProgress: boolean;
  showLoadingIndicator: boolean;
  loadingPhase: 'intro' | 'main' | 'completed' | 'error';
  messageType: 'generation' | 'regeneration';
}

// Canvas and viewport interfaces
export interface CanvasViewport {
  zoom: number;
  x: number;
  y: number;
  isDragging: boolean;
  lastMouseX: number;
  lastMouseY: number;
}

export interface CanvasPoint {
  x: number;
  y: number;
}

// Artifact interfaces
export interface ArtifactTypewriterState {
  visibleContent: string;
  isTyping: boolean;
  fullContent: string;
}

// Project interfaces
export interface ProjectInfo {
  id: string;
  name: string;
  description?: string;
  type?: string;
  status?: string;
  lastModified?: Date;
  createdDate?: Date;
}

// Generation interfaces
export interface GenerationOptions {
  userRequest: string;
  projectId: string;
  jobId: string;
  regenerationType?: 'direct' | 'sequential';
  imageDataUri?: string;
  applicationTarget?: 'mobile' | 'web';
  docsContent?: string;
}

export interface GenerationResult {
  success: boolean;
  files?: FileModel[];
  nodes?: UIDesignNode[];
  pages?: any[];
  error?: any;
}

export interface GenerationState {
  isInProgress: boolean;
  isComplete: boolean;
  startTime?: number;
  progressDescription: string;
  currentPhase: string;
  hasError: boolean;
  errorMessage: string | null;
}

// Search interfaces
export interface SearchMatch {
  lineNumber: number;
  lineContent: string;
  matchStart: number;
  matchEnd: number;
  matchText: string;
}

export interface FileSearchResult {
  file: FileModel;
  matches: SearchMatch[];
  totalMatches: number;
  expanded?: boolean;
}

// Common utility types
export interface ComponentState {
  isCodeGenerationComplete: boolean;
  isPromptBarEnabled: boolean;
  userSelectedTab: boolean;
  pollingStatus: string;
  currentProgressState: string;
  lastProgressDescription: string;
}

// Export all interfaces for easy importing
export * from './code-window-shared.interfaces';
