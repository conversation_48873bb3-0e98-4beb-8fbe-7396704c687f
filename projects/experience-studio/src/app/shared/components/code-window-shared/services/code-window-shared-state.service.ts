import { Injectable, signal, computed } from '@angular/core';
import { BehaviorSubject, Observable, combineLatest } from 'rxjs';
import { map, distinctUntilChanged } from 'rxjs/operators';
import { 
  ViewType, 
  ThemeType, 
  TabTransitionState, 
  FileModel,
  ComponentState,
  LoadingStates
} from '../interfaces/code-window-shared.interfaces';

@Injectable({
  providedIn: 'root'
})
export class CodeWindowSharedStateService {

  // Core state subjects
  private files$ = new BehaviorSubject<FileModel[]>([]);
  private isResizing$ = new BehaviorSubject<boolean>(false);
  private currentView$ = new BehaviorSubject<ViewType>('preview');
  private isLoading$ = new BehaviorSubject<boolean>(true);
  private currentTheme$ = new BehaviorSubject<ThemeType>('light');
  
  // Panel state subjects
  private isPanelCollapsed$ = new BehaviorSubject<boolean>(false);
  private isLeftPanelCollapsed$ = new BehaviorSubject<boolean>(false);
  private minWidth$ = new BehaviorSubject<string>('300px');
  private shouldHideProjectName$ = new BehaviorSubject<boolean>(false);
  private isExperienceStudioModalOpen$ = new BehaviorSubject<boolean>(false);

  // Tab state subjects
  private isHistoryActive$ = new BehaviorSubject<boolean>(false);
  private isCodeActive$ = new BehaviorSubject<boolean>(false);
  private isPreviewActive$ = new BehaviorSubject<boolean>(true);
  private isArtifactsActive$ = new BehaviorSubject<boolean>(false);
  private tabTransitionInProgress$ = new BehaviorSubject<boolean>(false);
  private currentTabState$ = new BehaviorSubject<TabTransitionState>({
    activeTab: 'preview',
    isTransitioning: false,
    lastError: null
  });

  // Component state subject
  private componentState$ = new BehaviorSubject<ComponentState>({
    isCodeGenerationComplete: false,
    isPromptBarEnabled: false,
    userSelectedTab: false,
    pollingStatus: 'PENDING',
    currentProgressState: '',
    lastProgressDescription: ''
  });

  // Loading states subject
  private loadingStates$ = new BehaviorSubject<LoadingStates>({
    isUIDesignLoading: false,
    isCodeGenerationLoading: false,
    isPreviewLoading: false,
    isArtifactsLoading: false,
    isRegenerationInProgress: false
  });

  // Signals for reactive state management
  readonly currentFiles = signal<FileModel[]>([]);
  readonly currentTheme = signal<ThemeType>('light');
  readonly isComponentLoading = signal<boolean>(true);
  readonly activeTabName = signal<string>('preview');

  // Computed signals
  readonly hasFiles = computed(() => this.currentFiles().length > 0);
  readonly isDarkTheme = computed(() => this.currentTheme() === 'dark');
  readonly isAnyTabActive = computed(() => 
    this.isCodeActive$.value || 
    this.isPreviewActive$.value || 
    this.isArtifactsActive$.value
  );

  // Observable getters
  readonly files = this.files$.asObservable();
  readonly isResizing = this.isResizing$.asObservable();
  readonly currentView = this.currentView$.asObservable();
  readonly isLoading = this.isLoading$.asObservable();
  readonly currentThemeObservable = this.currentTheme$.asObservable();
  readonly isPanelCollapsed = this.isPanelCollapsed$.asObservable();
  readonly isLeftPanelCollapsed = this.isLeftPanelCollapsed$.asObservable();
  readonly minWidth = this.minWidth$.asObservable();
  readonly shouldHideProjectName = this.shouldHideProjectName$.asObservable();
  readonly isExperienceStudioModalOpen = this.isExperienceStudioModalOpen$.asObservable();
  readonly isHistoryActive = this.isHistoryActive$.asObservable();
  readonly isCodeActive = this.isCodeActive$.asObservable();
  readonly isPreviewActive = this.isPreviewActive$.asObservable();
  readonly isArtifactsActive = this.isArtifactsActive$.asObservable();
  readonly tabTransitionInProgress = this.tabTransitionInProgress$.asObservable();
  readonly currentTabState = this.currentTabState$.asObservable();
  readonly componentState = this.componentState$.asObservable();
  readonly loadingStates = this.loadingStates$.asObservable();

  // Combined state observables
  readonly tabStates = combineLatest([
    this.isCodeActive$,
    this.isPreviewActive$,
    this.isArtifactsActive$,
    this.isHistoryActive$
  ]).pipe(
    map(([code, preview, artifacts, history]) => ({
      code,
      preview,
      artifacts,
      history
    })),
    distinctUntilChanged()
  );

  readonly panelStates = combineLatest([
    this.isPanelCollapsed$,
    this.isLeftPanelCollapsed$,
    this.minWidth$
  ]).pipe(
    map(([collapsed, leftCollapsed, minWidth]) => ({
      collapsed,
      leftCollapsed,
      minWidth
    })),
    distinctUntilChanged()
  );

  constructor() {
    this.initializeStateSync();
  }

  // State update methods
  setFiles(files: FileModel[]): void {
    this.files$.next(files);
    this.currentFiles.set(files);
  }

  getCurrentFiles(): FileModel[] {
    return this.files$.value;
  }

  setCurrentView(view: ViewType): void {
    this.currentView$.next(view);
    this.updateActiveTabFromView(view);
  }

  getCurrentView(): ViewType {
    return this.currentView$.value;
  }

  setLoading(loading: boolean): void {
    this.isLoading$.next(loading);
    this.isComponentLoading.set(loading);
  }

  setTheme(theme: ThemeType): void {
    this.currentTheme$.next(theme);
    this.currentTheme.set(theme);
  }

  setResizing(resizing: boolean): void {
    this.isResizing$.next(resizing);
  }

  // Panel state methods
  setPanelCollapsed(collapsed: boolean): void {
    this.isPanelCollapsed$.next(collapsed);
  }

  setLeftPanelCollapsed(collapsed: boolean): void {
    this.isLeftPanelCollapsed$.next(collapsed);
  }

  setMinWidth(width: string): void {
    this.minWidth$.next(width);
  }

  setShouldHideProjectName(hide: boolean): void {
    this.shouldHideProjectName$.next(hide);
  }

  setExperienceStudioModalOpen(open: boolean): void {
    this.isExperienceStudioModalOpen$.next(open);
  }

  // Tab state methods
  setHistoryActive(active: boolean): void {
    this.isHistoryActive$.next(active);
    if (active) {
      this.deactivateOtherTabs('history');
    }
  }

  setCodeActive(active: boolean): void {
    this.isCodeActive$.next(active);
    if (active) {
      this.deactivateOtherTabs('code');
      this.setCurrentView('editor');
    }
  }

  setPreviewActive(active: boolean): void {
    this.isPreviewActive$.next(active);
    if (active) {
      this.deactivateOtherTabs('preview');
      this.setCurrentView('preview');
    }
  }

  setArtifactsActive(active: boolean): void {
    this.isArtifactsActive$.next(active);
    if (active) {
      this.deactivateOtherTabs('artifacts');
      this.setCurrentView('artifacts');
    }
  }

  setTabTransitionInProgress(inProgress: boolean): void {
    this.tabTransitionInProgress$.next(inProgress);
  }

  setCurrentTabState(state: TabTransitionState): void {
    this.currentTabState$.next(state);
    this.activeTabName.set(state.activeTab);
  }

  // Component state methods
  setComponentState(state: Partial<ComponentState>): void {
    const currentState = this.componentState$.value;
    this.componentState$.next({ ...currentState, ...state });
  }

  getComponentState(): ComponentState {
    return this.componentState$.value;
  }

  // Loading states methods
  setLoadingStates(states: Partial<LoadingStates>): void {
    const currentStates = this.loadingStates$.value;
    this.loadingStates$.next({ ...currentStates, ...states });
  }

  getLoadingStates(): LoadingStates {
    return this.loadingStates$.value;
  }

  // Reset methods
  resetAllState(): void {
    this.setFiles([]);
    this.setCurrentView('preview');
    this.setLoading(true);
    this.setResizing(false);
    this.resetTabStates();
    this.resetPanelStates();
    this.resetComponentState();
    this.resetLoadingStates();
  }

  resetTabStates(): void {
    this.setHistoryActive(false);
    this.setCodeActive(false);
    this.setPreviewActive(true);
    this.setArtifactsActive(false);
    this.setTabTransitionInProgress(false);
    this.setCurrentTabState({
      activeTab: 'preview',
      isTransitioning: false,
      lastError: null
    });
  }

  resetPanelStates(): void {
    this.setPanelCollapsed(false);
    this.setLeftPanelCollapsed(false);
    this.setMinWidth('300px');
    this.setShouldHideProjectName(false);
    this.setExperienceStudioModalOpen(false);
  }

  resetComponentState(): void {
    this.setComponentState({
      isCodeGenerationComplete: false,
      isPromptBarEnabled: false,
      userSelectedTab: false,
      pollingStatus: 'PENDING',
      currentProgressState: '',
      lastProgressDescription: ''
    });
  }

  resetLoadingStates(): void {
    this.setLoadingStates({
      isUIDesignLoading: false,
      isCodeGenerationLoading: false,
      isPreviewLoading: false,
      isArtifactsLoading: false,
      isRegenerationInProgress: false
    });
  }

  // Private helper methods
  private deactivateOtherTabs(activeTab: string): void {
    if (activeTab !== 'history') this.isHistoryActive$.next(false);
    if (activeTab !== 'code') this.isCodeActive$.next(false);
    if (activeTab !== 'preview') this.isPreviewActive$.next(false);
    if (activeTab !== 'artifacts') this.isArtifactsActive$.next(false);
  }

  private updateActiveTabFromView(view: ViewType): void {
    switch (view) {
      case 'editor':
        this.setCodeActive(true);
        break;
      case 'preview':
        this.setPreviewActive(true);
        break;
      case 'artifacts':
        this.setArtifactsActive(true);
        break;
      case 'overview':
        // Overview is typically handled by preview tab
        this.setPreviewActive(true);
        break;
      default:
        break;
    }
  }

  private initializeStateSync(): void {
    // Sync signals with observables
    this.currentTheme$.subscribe(theme => this.currentTheme.set(theme));
    this.isLoading$.subscribe(loading => this.isComponentLoading.set(loading));
    this.files$.subscribe(files => this.currentFiles.set(files));
    
    // Sync tab state with active tab name
    this.currentTabState$.subscribe(state => this.activeTabName.set(state.activeTab));
  }
}
