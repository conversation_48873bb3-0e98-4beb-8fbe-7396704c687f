import { Injectable, signal, computed, inject } from '@angular/core';
import { BehaviorSubject, Observable } from 'rxjs';
import { distinctUntilChanged } from 'rxjs/operators';
import { ThemeType } from '../interfaces/code-window-shared.interfaces';
import { CodeWindowSharedUtilsService } from './code-window-shared-utils.service';

@Injectable({
  providedIn: 'root'
})
export class CodeWindowSharedThemeService {
  private readonly utilsService = inject(CodeWindowSharedUtilsService);

  // Theme state
  private currentTheme$ = new BehaviorSubject<ThemeType>('light');
  private isThemeTransitioning$ = new BehaviorSubject<boolean>(false);
  private themePreference$ = new BehaviorSubject<'auto' | 'light' | 'dark'>('auto');

  // Signals for reactive theme management
  readonly currentTheme = signal<ThemeType>('light');
  readonly isTransitioning = signal<boolean>(false);
  readonly themePreference = signal<'auto' | 'light' | 'dark'>('auto');

  // Computed signals
  readonly isDarkTheme = computed(() => this.currentTheme() === 'dark');
  readonly isLightTheme = computed(() => this.currentTheme() === 'light');
  readonly themeClass = computed(() => this.utilsService.getThemeClass(this.currentTheme()));
  readonly canToggleTheme = computed(() => !this.isTransitioning());

  // Observable getters
  readonly themeObservable = this.currentTheme$.asObservable().pipe(distinctUntilChanged());
  readonly isThemeTransitioningObservable = this.isThemeTransitioning$.asObservable();
  readonly themePreferenceObservable = this.themePreference$.asObservable();

  // Constants
  private readonly THEME_STORAGE_KEY = 'code-window-theme-preference';
  private readonly THEME_TRANSITION_DURATION = 300; // milliseconds

  constructor() {
    this.initializeTheme();
    this.setupSystemThemeListener();
    this.syncSignalsWithObservables();
  }

  // Public methods
  setTheme(theme: ThemeType): void {
    if (this.isThemeTransitioning$.value) {
      return; // Prevent theme changes during transition
    }

    this.startThemeTransition();
    
    setTimeout(() => {
      this.currentTheme$.next(theme);
      this.currentTheme.set(theme);
      this.saveThemePreference(theme);
      this.applyThemeToDocument(theme);
      this.endThemeTransition();
    }, 50); // Small delay to ensure smooth transition
  }

  toggleTheme(): void {
    const newTheme = this.currentTheme$.value === 'light' ? 'dark' : 'light';
    this.setTheme(newTheme);
  }

  setThemePreference(preference: 'auto' | 'light' | 'dark'): void {
    this.themePreference$.next(preference);
    this.themePreference.set(preference);
    this.saveThemePreference(preference);

    if (preference === 'auto') {
      this.applySystemTheme();
    } else {
      this.setTheme(preference);
    }
  }

  getCurrentTheme(): ThemeType {
    return this.currentTheme$.value;
  }

  getThemePreference(): 'auto' | 'light' | 'dark' {
    return this.themePreference$.value;
  }

  // System theme detection
  getSystemTheme(): ThemeType {
    if (typeof window !== 'undefined' && window.matchMedia) {
      return window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';
    }
    return 'light'; // Default fallback
  }

  isSystemThemeSupported(): boolean {
    return typeof window !== 'undefined' && 
           window.matchMedia && 
           window.matchMedia('(prefers-color-scheme)').media !== 'not all';
  }

  // Theme utilities
  getThemeColors(theme?: ThemeType): Record<string, string> {
    const targetTheme = theme || this.currentTheme$.value;
    
    if (targetTheme === 'dark') {
      return {
        primary: '#ffffff',
        secondary: '#e5e7eb',
        background: '#1f2937',
        surface: '#374151',
        border: '#4b5563',
        text: '#ffffff',
        textSecondary: '#d1d5db',
        accent: '#3b82f6',
        success: '#10b981',
        warning: '#f59e0b',
        error: '#ef4444'
      };
    }

    return {
      primary: '#1f2937',
      secondary: '#6b7280',
      background: '#ffffff',
      surface: '#f9fafb',
      border: '#e5e7eb',
      text: '#1f2937',
      textSecondary: '#6b7280',
      accent: '#3b82f6',
      success: '#10b981',
      warning: '#f59e0b',
      error: '#ef4444'
    };
  }

  getContrastColor(backgroundColor: string): string {
    // Simple contrast calculation
    const hex = backgroundColor.replace('#', '');
    const r = parseInt(hex.substr(0, 2), 16);
    const g = parseInt(hex.substr(2, 2), 16);
    const b = parseInt(hex.substr(4, 2), 16);
    const brightness = (r * 299 + g * 587 + b * 114) / 1000;
    return brightness > 128 ? '#000000' : '#ffffff';
  }

  // CSS custom properties management
  updateCSSCustomProperties(theme?: ThemeType): void {
    const targetTheme = theme || this.currentTheme$.value;
    const colors = this.getThemeColors(targetTheme);
    
    if (typeof document !== 'undefined') {
      const root = document.documentElement;
      
      Object.entries(colors).forEach(([key, value]) => {
        root.style.setProperty(`--theme-${key}`, value);
      });
      
      root.style.setProperty('--theme-name', targetTheme);
    }
  }

  // Private methods
  private initializeTheme(): void {
    const savedPreference = this.loadThemePreference();
    this.themePreference$.next(savedPreference);
    this.themePreference.set(savedPreference);

    if (savedPreference === 'auto') {
      this.applySystemTheme();
    } else {
      this.setTheme(savedPreference);
    }
  }

  private setupSystemThemeListener(): void {
    if (this.isSystemThemeSupported()) {
      const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
      
      mediaQuery.addEventListener('change', (e) => {
        if (this.themePreference$.value === 'auto') {
          const systemTheme = e.matches ? 'dark' : 'light';
          this.setTheme(systemTheme);
        }
      });
    }
  }

  private applySystemTheme(): void {
    const systemTheme = this.getSystemTheme();
    this.setTheme(systemTheme);
  }

  private applyThemeToDocument(theme: ThemeType): void {
    if (typeof document !== 'undefined') {
      document.documentElement.setAttribute('data-theme', theme);
      document.body.className = document.body.className.replace(/\b(light|dark)-theme\b/g, '');
      document.body.classList.add(`${theme}-theme`);
      this.updateCSSCustomProperties(theme);
    }
  }

  private startThemeTransition(): void {
    this.isThemeTransitioning$.next(true);
    this.isTransitioning.set(true);
  }

  private endThemeTransition(): void {
    setTimeout(() => {
      this.isThemeTransitioning$.next(false);
      this.isTransitioning.set(false);
    }, this.THEME_TRANSITION_DURATION);
  }

  private saveThemePreference(preference: 'auto' | 'light' | 'dark'): void {
    this.utilsService.setLocalStorageItem(this.THEME_STORAGE_KEY, preference);
  }

  private loadThemePreference(): 'auto' | 'light' | 'dark' {
    const saved = this.utilsService.getLocalStorageItem<'auto' | 'light' | 'dark'>(
      this.THEME_STORAGE_KEY, 
      'auto'
    );
    return saved || 'auto';
  }

  private syncSignalsWithObservables(): void {
    this.currentTheme$.subscribe(theme => this.currentTheme.set(theme));
    this.isThemeTransitioning$.subscribe(transitioning => this.isTransitioning.set(transitioning));
    this.themePreference$.subscribe(preference => this.themePreference.set(preference));
  }

  // Theme validation
  isValidTheme(theme: string): theme is ThemeType {
    return theme === 'light' || theme === 'dark';
  }

  // Reset theme to default
  resetTheme(): void {
    this.setThemePreference('auto');
  }

  // Export theme configuration
  exportThemeConfig(): { theme: ThemeType; preference: string; colors: Record<string, string> } {
    return {
      theme: this.currentTheme$.value,
      preference: this.themePreference$.value,
      colors: this.getThemeColors()
    };
  }
}
