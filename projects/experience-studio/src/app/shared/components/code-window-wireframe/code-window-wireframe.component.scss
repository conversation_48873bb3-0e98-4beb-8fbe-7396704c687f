// Code Window Wireframe Component Styles
.wireframe-content-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  width: 100%;
  position: relative;
  background-color: var(--theme-background, #ffffff);
  overflow: hidden;

  // Wireframe Loading Container
  .wireframe-loading-container {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
    padding: 2rem;

    .wireframe-typewriter-container {
      max-width: 600px;
      width: 100%;

      .primary-typewriter-wrapper {
        text-align: center;
      }
    }
  }

  // UI Design Canvas Container
  .ui-design-canvas-container {
    position: relative;
    height: 100%;
    width: 100%;
    overflow: hidden;
    background: linear-gradient(
      45deg,
      transparent 24%,
      rgba(0, 0, 0, 0.03) 25%,
      rgba(0, 0, 0, 0.03) 26%,
      transparent 27%,
      transparent 74%,
      rgba(0, 0, 0, 0.03) 75%,
      rgba(0, 0, 0, 0.03) 76%,
      transparent 77%
    );
    background-size: 20px 20px;

    // Canvas Selection Tooltip
    .canvas-selection-tooltip {
      position: absolute;
      top: 20px;
      left: 50%;
      transform: translateX(-50%);
      z-index: 1000;
      background-color: var(--theme-surface, #f9fafb);
      border: 1px solid var(--theme-border, #e5e7eb);
      border-radius: 8px;
      padding: 0.75rem 1rem;
      box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
      animation: fadeInDown 0.3s ease-out;

      .tooltip-content {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        font-size: 0.875rem;
        color: var(--theme-textSecondary, #6b7280);

        i {
          color: var(--theme-accent, #3b82f6);
        }
      }

      &.dark-theme {
        background-color: var(--theme-surface, #374151);
        border-color: var(--theme-border, #4b5563);
      }
    }

    // UI Design Nodes Container
    .ui-design-nodes-container {
      position: relative;
      height: 100%;
      width: 100%;
      transform-origin: 0 0;

      .ui-design-node {
        position: absolute;
        border: 2px solid transparent;
        border-radius: 8px;
        background-color: var(--theme-background, #ffffff);
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
        cursor: pointer;
        transition: all 0.2s ease;
        overflow: hidden;

        &:hover {
          box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
          transform: translateY(-2px);
        }

        &.selected {
          border-color: var(--theme-accent, #3b82f6);
          box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }

        &.dragging {
          z-index: 1000;
          transform: rotate(2deg);
          box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
        }

        .node-header {
          padding: 0.75rem 1rem;
          background-color: var(--theme-surface, #f9fafb);
          border-bottom: 1px solid var(--theme-border, #e5e7eb);

          h4 {
            margin: 0;
            font-size: 0.875rem;
            font-weight: 600;
            color: var(--theme-text, #1f2937);
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;

            &.highlighted-title {
              color: var(--theme-accent, #3b82f6);
            }
          }
        }

        .node-content {
          position: relative;
          height: calc(100% - 60px); // Subtract header height
          overflow: hidden;

          .node-loader {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            height: 100%;
            padding: 2rem;
            background-color: var(--theme-surface, #f9fafb);

            .loader-spinner {
              width: 32px;
              height: 32px;
              border: 3px solid var(--theme-border, #e5e7eb);
              border-top: 3px solid var(--theme-accent, #3b82f6);
              border-radius: 50%;
              animation: spin 1s linear infinite;
              margin-bottom: 1rem;
            }

            .loader-text {
              font-size: 0.875rem;
              color: var(--theme-textSecondary, #6b7280);
              text-align: center;
            }
          }

          .preview-iframe {
            width: 100%;
            height: 100%;
            border: none;
            background-color: white;
          }
        }
      }
    }
  }

  // Error Container
  .wireframe-error-container {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
    padding: 2rem;

    .error-content {
      text-align: center;
      max-width: 400px;

      .error-icon {
        font-size: 3rem;
        color: var(--theme-error, #ef4444);
        margin-bottom: 1rem;
      }

      h3 {
        margin: 0 0 0.5rem 0;
        font-size: 1.25rem;
        font-weight: 600;
        color: var(--theme-text, #1f2937);
      }

      p {
        margin: 0 0 1.5rem 0;
        color: var(--theme-textSecondary, #6b7280);
        line-height: 1.5;
      }

      .retry-button {
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
        padding: 0.75rem 1.5rem;
        background-color: var(--theme-accent, #3b82f6);
        color: white;
        border: none;
        border-radius: 6px;
        font-weight: 500;
        cursor: pointer;
        transition: background-color 0.2s ease;

        &:hover {
          background-color: var(--theme-accent-hover, #2563eb);
        }

        i {
          font-size: 1rem;
        }
      }
    }
  }

  // Empty Container
  .wireframe-empty-container {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
    padding: 2rem;

    .empty-content {
      text-align: center;
      max-width: 400px;

      .empty-icon {
        font-size: 3rem;
        color: var(--theme-textSecondary, #6b7280);
        margin-bottom: 1rem;
      }

      h3 {
        margin: 0 0 0.5rem 0;
        font-size: 1.25rem;
        font-weight: 600;
        color: var(--theme-text, #1f2937);
      }

      p {
        margin: 0;
        color: var(--theme-textSecondary, #6b7280);
        line-height: 1.5;
      }
    }
  }

  // Dark theme overrides
  &.dark-theme {
    .ui-design-canvas-container {
      background: linear-gradient(
        45deg,
        transparent 24%,
        rgba(255, 255, 255, 0.03) 25%,
        rgba(255, 255, 255, 0.03) 26%,
        transparent 27%,
        transparent 74%,
        rgba(255, 255, 255, 0.03) 75%,
        rgba(255, 255, 255, 0.03) 76%,
        transparent 77%
      );

      .ui-design-node {
        background-color: var(--theme-surface, #374151);

        .node-header {
          background-color: var(--theme-background, #1f2937);
          border-bottom-color: var(--theme-border, #4b5563);
        }

        .node-content .node-loader {
          background-color: var(--theme-background, #1f2937);
        }
      }
    }
  }
}

// Animations
@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

@keyframes fadeInDown {
  0% {
    opacity: 0;
    transform: translateX(-50%) translateY(-10px);
  }
  100% {
    opacity: 1;
    transform: translateX(-50%) translateY(0);
  }
}

// Responsive design
@media (max-width: 768px) {
  .wireframe-content-container {
    .ui-design-canvas-container {
      .canvas-selection-tooltip {
        top: 10px;
        left: 10px;
        right: 10px;
        transform: none;

        .tooltip-content {
          font-size: 0.8rem;
        }
      }

      .ui-design-node {
        .node-header h4 {
          font-size: 0.8rem;
        }
      }
    }

    .wireframe-error-container,
    .wireframe-empty-container {
      padding: 1rem;

      .error-content,
      .empty-content {
        .error-icon,
        .empty-icon {
          font-size: 2rem;
        }

        h3 {
          font-size: 1.1rem;
        }

        p {
          font-size: 0.875rem;
        }
      }
    }
  }
}

// Reduced motion support
@media (prefers-reduced-motion: reduce) {
  .wireframe-content-container {
    * {
      transition: none !important;
      animation: none !important;
    }
  }
}
