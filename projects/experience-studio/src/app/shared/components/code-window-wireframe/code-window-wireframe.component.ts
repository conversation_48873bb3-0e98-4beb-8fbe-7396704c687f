import {
  Component,
  Input,
  Output,
  EventEmitter,
  OnInit,
  On<PERSON>estroy,
  AfterViewInit,
  ViewChild,
  ElementRef,
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  inject,
  signal,
  computed
} from '@angular/core';
import { CommonModule } from '@angular/common';
import { Subject, BehaviorSubject, Observable, combineLatest } from 'rxjs';
import { takeUntil, filter, map } from 'rxjs/operators';
import { DomSanitizer, SafeHtml } from '@angular/platform-browser';

// Shared imports
import {
  CodeWindowSharedComponent,
  CodeWindowSharedStateService,
  CodeWindowSharedThemeService,
  CodeWindowSharedUtilsService,
  UIDesignNode,
  UIDesignNodeData,
  UIDesignPageData,
  ViewType,
  ThemeType,
  TabConfig
} from '../code-window-shared';

// Wireframe-specific services (these would be moved from the original component)
// Note: These services would be moved to a wireframe-specific services directory
// For now, importing from the original location
import { UIDesignCanvasService } from '../code-window/services/ui-design-canvas.service';
import { UIDesignNodeService } from '../code-window/services/ui-design-node.service';
import { UIDesignViewportService } from '../code-window/services/ui-design-viewport.service';
import { WireframeGenerationStateService } from '../../services/wireframe-generation-state.service';
import { GenerateUIDesignService } from '../../services/generate-ui-design.service';
import { UIDesignSelectionService } from '../../services/ui-design-selection.service';
import { UIDesignEditService } from '../../services/ui-design-edit.service';
import { UIDesignVisualFeedbackService } from '../../services/ui-design-visual-feedback.service';
import { WireframeNodeManagementService } from '../../services/wireframe-node-management.service';
import { UIDesignIntroService } from '../../services/ui-design-intro.service';

// Components
import { CanvasInfoComponent } from '../code-window/components/canvas-info/canvas-info.component';
import { MobileFrameComponent, MobilePage } from '../mobile-frame/mobile-frame.component';
import { WebFrameComponent, WebPage } from '../web-frame/web-frame.component';
import { WireframeTypewriterComponent } from '../code-window/components/wireframe-typewriter/wireframe-typewriter.component';
import { SafeSrcdocDirective } from '../../directives/safe-srcdoc.directive';

export interface WireframeGenerationOptions {
  userRequest: string;
  projectId: string;
  jobId: string;
  imageDataUri?: string;
  applicationTarget?: 'mobile' | 'web';
  docsContent?: string;
}

export interface WireframeGenerationResult {
  success: boolean;
  nodes: UIDesignNode[];
  pages: MobilePage[];
  error?: any;
}

@Component({
  selector: 'app-code-window-wireframe',
  standalone: true,
  imports: [
    CommonModule,
    CodeWindowSharedComponent,
    CanvasInfoComponent,
    MobileFrameComponent,
    WebFrameComponent,
    WireframeTypewriterComponent,
    SafeSrcdocDirective
  ],
  template: `
    <app-code-window-shared
      [projectName]="projectName"
      [showHeader]="showHeader"
      [showTabs]="showTabs"
      [availableTabs]="wireframeTabs"
      [loadingMessage]="loadingMessage"
      [errorMessage]="errorMessage"
      (tabChanged)="onTabChanged($event)"
      (themeChanged)="onThemeChanged($event)"
      (errorDismissed)="onErrorDismissed()"
    >
      <!-- Header content -->
      <div slot="header-left">
        <ng-content select="[slot=header-left]"></ng-content>
      </div>

      <div slot="header-center">
        <ng-content select="[slot=header-center]"></ng-content>
      </div>

      <div slot="header-right">
        <ng-content select="[slot=header-right]"></ng-content>
      </div>

      <!-- Main content area -->
      <div class="wireframe-content-container" [class]="themeService.themeClass()">

        <!-- Wireframe Generation Loading State -->
        <div
          *ngIf="isGenerating() && !hasNodes()"
          class="wireframe-loading-container"
        >
          <div class="wireframe-typewriter-container">
            <div class="primary-typewriter-wrapper">
              <app-wireframe-typewriter></app-wireframe-typewriter>
            </div>
          </div>
        </div>

        <!-- UI Design Canvas Mode -->
        <div
          *ngIf="currentView() === 'overview' && hasNodes()"
          class="ui-design-canvas-container"
          #uiDesignCanvas
        >
          <!-- Canvas Info Panel -->
          <app-canvas-info></app-canvas-info>

          <!-- Canvas Selection Tooltip -->
          <div
            *ngIf="showCanvasTooltip()"
            class="canvas-selection-tooltip"
            [class]="themeService.themeClass()"
          >
            <div class="tooltip-content">
              <i class="bi bi-cursor-fill"></i>
              <span>Single click to select a page for editing</span>
            </div>
          </div>

          <!-- UI Design Nodes -->
          <div class="ui-design-nodes-container">
            <div
              *ngFor="let node of uiDesignNodes(); trackBy: trackByNodeId"
              class="ui-design-node"
              [class.selected]="isNodeSelected(node.id)"
              [class.dragging]="node.dragging"
              [style.transform]="getNodeTransform(node)"
              [style.width.px]="node.data.width"
              [style.height.px]="node.data.height"
              [style.z-index]="node.data.zIndex || 1"
              (click)="onNodeClick(node, $event)"
              (mousedown)="onNodeMouseDown(node, $event)"
            >
              <div class="node-header">
                <h4
                  [class.highlighted-title]="isNodeSelected(node.id)"
                  [attr.aria-label]="node.data.displayTitle || node.data.title"
                >
                  {{ node.data.displayTitle || node.data.title }}
                </h4>
              </div>

              <div class="node-content">
                <div *ngIf="node.data.isLoading" class="node-loader">
                  <div class="loader-spinner"></div>
                  <div class="loader-text">Generating wireframe...</div>
                </div>

                <iframe
                  *ngIf="!node.data.isLoading"
                  [safeSrcdoc]="node.data.rawContent"
                  [isWireframeContent]="true"
                  class="preview-iframe"
                  frameborder="0"
                  sandbox="allow-same-origin allow-scripts allow-top-navigation-by-user-activation"
                >
                </iframe>
              </div>
            </div>
          </div>
        </div>

        <!-- Mobile Frame View -->
        <div *ngIf="currentView() === 'preview' && currentViewportMode() === 'mobile'">
          <app-mobile-frame
            [pages]="mobilePages()"
            [currentPageIndex]="currentPageIndex()"
            [theme]="themeService.currentTheme()"
            (pageChange)="onPageChange($event)"
            (fullscreenRequest)="onFullscreenRequest($event)"
          >
          </app-mobile-frame>
        </div>

        <!-- Web Frame View -->
        <div *ngIf="currentView() === 'preview' && currentViewportMode() === 'web'">
          <app-web-frame
            [pages]="webPages()"
            [currentPageIndex]="currentPageIndex()"
            [theme]="themeService.currentTheme()"
            (pageChange)="onPageChange($event)"
            (fullscreenRequest)="onFullscreenRequest($event)"
          >
          </app-web-frame>
        </div>

        <!-- Error State -->
        <div *ngIf="hasError() && !isGenerating()" class="wireframe-error-container">
          <div class="error-content">
            <i class="bi bi-exclamation-triangle error-icon"></i>
            <h3>Wireframe Generation Failed</h3>
            <p>{{ errorMessage || 'An error occurred while generating wireframes.' }}</p>
            <button class="retry-button" (click)="retryGeneration()">
              <i class="bi bi-arrow-clockwise"></i>
              Retry Generation
            </button>
          </div>
        </div>

        <!-- Empty State -->
        <div *ngIf="!isGenerating() && !hasNodes() && !hasError()" class="wireframe-empty-container">
          <div class="empty-content">
            <i class="bi bi-file-earmark-plus empty-icon"></i>
            <h3>No Wireframes Generated</h3>
            <p>Start by generating wireframes from your design requirements.</p>
          </div>
        </div>

      </div>
    </app-code-window-shared>
  `,
  styleUrls: ['./code-window-wireframe.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class CodeWindowWireframeComponent implements OnInit, AfterViewInit, OnDestroy {
  // Injected services
  private readonly cdr = inject(ChangeDetectorRef);
  private readonly sanitizer = inject(DomSanitizer);
  readonly sharedStateService = inject(CodeWindowSharedStateService);
  readonly themeService = inject(CodeWindowSharedThemeService);
  readonly utilsService = inject(CodeWindowSharedUtilsService);

  // Wireframe-specific services
  private readonly canvasService = inject(UIDesignCanvasService);
  private readonly nodeService = inject(UIDesignNodeService);
  private readonly viewportService = inject(UIDesignViewportService);
  private readonly generationStateService = inject(WireframeGenerationStateService);
  private readonly generateUIDesignService = inject(GenerateUIDesignService);
  private readonly selectionService = inject(UIDesignSelectionService);
  private readonly editService = inject(UIDesignEditService);
  private readonly visualFeedbackService = inject(UIDesignVisualFeedbackService);
  private readonly nodeManagementService = inject(WireframeNodeManagementService);
  private readonly introService = inject(UIDesignIntroService);

  // ViewChild references
  @ViewChild('uiDesignCanvas', { static: false }) uiDesignCanvas!: ElementRef;

  // Inputs
  @Input() projectName: string | null = null;
  @Input() showHeader: boolean = true;
  @Input() showTabs: boolean = true;
  @Input() initialViewportMode: 'mobile' | 'web' = 'mobile';

  // Outputs
  @Output() wireframeGenerated = new EventEmitter<WireframeGenerationResult>();
  @Output() wireframeError = new EventEmitter<any>();
  @Output() nodeSelected = new EventEmitter<UIDesignNode>();
  @Output() viewportModeChanged = new EventEmitter<'mobile' | 'web'>();

  // Component state
  private destroy$ = new Subject<void>();

  // Reactive state
  private uiDesignNodes$ = new BehaviorSubject<UIDesignNode[]>([]);
  private mobilePages$ = new BehaviorSubject<MobilePage[]>([]);
  private webPages$ = new BehaviorSubject<WebPage[]>([]);
  private currentPageIndex$ = new BehaviorSubject<number>(0);
  private currentViewportMode$ = new BehaviorSubject<'mobile' | 'web'>('mobile');
  private showCanvasTooltip$ = new BehaviorSubject<boolean>(true);
  private selectedNodeIds$ = new BehaviorSubject<string[]>([]);

  // Signals
  readonly uiDesignNodes = signal<UIDesignNode[]>([]);
  readonly mobilePages = signal<MobilePage[]>([]);
  readonly webPages = signal<WebPage[]>([]);
  readonly currentPageIndex = signal<number>(0);
  readonly currentViewportMode = signal<'mobile' | 'web'>('mobile');
  readonly showCanvasTooltip = signal<boolean>(true);
  readonly isGenerating = signal<boolean>(false);
  readonly hasError = signal<boolean>(false);
  readonly currentView = signal<ViewType>('overview');
  readonly loadingMessage = signal<string>('Generating wireframes...');
  readonly errorMessage = signal<string | null>(null);

  // Computed properties
  readonly hasNodes = computed(() => this.uiDesignNodes().length > 0);
  readonly canShowCanvas = computed(() => this.hasNodes() && this.currentView() === 'overview');
  readonly canShowPreview = computed(() => this.hasNodes() && this.currentView() === 'preview');

  // Tab configuration
  readonly wireframeTabs: TabConfig[] = [
    {
      id: 'overview',
      label: 'Overview',
      icon: 'bi bi-grid-3x3',
      active: true,
      disabled: false,
      loading: false,
      tooltip: 'View wireframe canvas'
    },
    {
      id: 'preview',
      label: 'Preview',
      icon: 'bi bi-eye',
      active: false,
      disabled: false,
      loading: false,
      tooltip: 'Preview wireframes'
    }
  ];

  ngOnInit(): void {
    this.initializeComponent();
    this.setupSubscriptions();
  }

  ngAfterViewInit(): void {
    this.setupCanvasInteractions();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  // Public methods
  generateWireframes(options: WireframeGenerationOptions): void {
    this.startGeneration();

    // Use the existing generation service
    this.generateUIDesignService.generateUIDesign({
      userRequest: options.userRequest,
      projectId: options.projectId,
      jobId: options.jobId,
      imageDataUri: options.imageDataUri,
      applicationTarget: options.applicationTarget,
      docsContent: options.docsContent
    }).subscribe({
      next: (response) => this.handleGenerationSuccess(response),
      error: (error) => this.handleGenerationError(error)
    });
  }

  retryGeneration(): void {
    this.hasError.set(false);
    this.errorMessage.set(null);
    // Emit event to parent to retry with last options
    // This would need to be implemented based on parent component needs
  }

  onTabChanged(tabId: string): void {
    this.currentView.set(tabId as ViewType);
    this.sharedStateService.setCurrentView(tabId as ViewType);
  }

  onThemeChanged(theme: ThemeType): void {
    // Handle theme changes if needed
  }

  onErrorDismissed(): void {
    this.hasError.set(false);
    this.errorMessage.set(null);
  }

  onNodeClick(node: UIDesignNode, event: MouseEvent): void {
    event.stopPropagation();
    this.selectNode(node);
  }

  onNodeMouseDown(node: UIDesignNode, event: MouseEvent): void {
    // Handle node dragging if needed
    event.preventDefault();
  }

  onPageChange(pageIndex: number): void {
    this.currentPageIndex.set(pageIndex);
    this.currentPageIndex$.next(pageIndex);
  }

  onFullscreenRequest(page: MobilePage | WebPage): void {
    // Handle fullscreen request
  }

  // Utility methods
  trackByNodeId(index: number, node: UIDesignNode): string {
    return node.id;
  }

  getNodeTransform(node: UIDesignNode): string {
    return `translate(${node.position.x}px, ${node.position.y}px)`;
  }

  isNodeSelected(nodeId: string): boolean {
    return this.selectedNodeIds$.value.includes(nodeId);
  }

  // Private methods
  private initializeComponent(): void {
    this.currentViewportMode.set(this.initialViewportMode);
    this.currentViewportMode$.next(this.initialViewportMode);
  }

  private setupSubscriptions(): void {
    // Subscribe to generation state
    this.generationStateService.isGenerating$
      .pipe(takeUntil(this.destroy$))
      .subscribe(isGenerating => {
        this.isGenerating.set(isGenerating);
        if (isGenerating) {
          this.loadingMessage.set('Generating wireframes...');
        }
      });

    // Subscribe to nodes
    this.nodeService.nodes
      .pipe(takeUntil(this.destroy$))
      .subscribe(nodes => {
        this.uiDesignNodes.set(nodes);
        this.uiDesignNodes$.next(nodes);
        this.updatePagesFromNodes(nodes);
      });

    // Subscribe to selection changes
    this.nodeService.selectedNodes
      .pipe(takeUntil(this.destroy$))
      .subscribe(selectedIds => {
        this.selectedNodeIds$.next(selectedIds);
      });

    // Sync signals with observables
    this.syncSignalsWithObservables();
  }

  private setupCanvasInteractions(): void {
    if (this.uiDesignCanvas) {
      // Setup canvas event listeners for panning, zooming, etc.
      // This would integrate with the viewport service
    }
  }

  private startGeneration(): void {
    this.isGenerating.set(true);
    this.hasError.set(false);
    this.errorMessage.set(null);
    this.generationStateService.startGeneration();
  }

  private handleGenerationSuccess(response: any): void {
    try {
      // Process the response and create nodes
      const nodes = this.processGenerationResponse(response);

      this.nodeService.setNodes(nodes);
      this.generationStateService.completeGeneration(nodes.length);
      this.isGenerating.set(false);

      const result: WireframeGenerationResult = {
        success: true,
        nodes,
        pages: this.mobilePages()
      };

      this.wireframeGenerated.emit(result);
    } catch (error) {
      this.handleGenerationError(error);
    }
  }

  private handleGenerationError(error: any): void {
    this.isGenerating.set(false);
    this.hasError.set(true);
    this.errorMessage.set(this.utilsService.extractErrorMessage(error));
    this.generationStateService.setError(this.utilsService.extractErrorMessage(error));
    this.wireframeError.emit(error);
  }

  private processGenerationResponse(response: any): UIDesignNode[] {
    // Convert API response to UI design nodes
    // This would use the existing processing logic from the original component
    return [];
  }

  private updatePagesFromNodes(nodes: UIDesignNode[]): void {
    const mobilePages: MobilePage[] = nodes.map(node => ({
      fileName: node.data.title,
      content: node.data.rawContent
    }));

    const webPages: WebPage[] = nodes.map(node => ({
      fileName: node.data.title,
      content: node.data.rawContent
    }));

    this.mobilePages.set(mobilePages);
    this.webPages.set(webPages);
    this.mobilePages$.next(mobilePages);
    this.webPages$.next(webPages);
  }

  private selectNode(node: UIDesignNode): void {
    this.selectionService.selectNode(node.id);
    this.nodeSelected.emit(node);
  }

  private syncSignalsWithObservables(): void {
    this.currentViewportMode$.subscribe(mode => this.currentViewportMode.set(mode));
    this.currentPageIndex$.subscribe(index => this.currentPageIndex.set(index));
    this.showCanvasTooltip$.subscribe(show => this.showCanvasTooltip.set(show));
  }
}
